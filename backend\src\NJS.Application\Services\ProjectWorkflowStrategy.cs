using Microsoft.EntityFrameworkCore;
using NJS.Application.CQRS.PMWorkflow.Commands;
using NJS.Application.Dtos;
using NJS.Application.Services.IContract;
using NJS.Domain.Database;
using NJS.Domain.Entities;
using NJS.Domain.Enums;

namespace NJS.Application.Services
{
    public class ProjectWorkflowStrategy : IEntityWorkflowStrategy
    {
        private readonly ProjectManagementContext _context;

        public ProjectWorkflowStrategy(ProjectManagementContext context)
        {
            _context = context;
        }
        
        public string EntityType => "Project";

        public async Task<PMWorkflowDto> ExecuteAsync(WorkflowActionContext context, CancellationToken cancellationToken)
        {
            var project = await _context.Projects
                .FirstOrDefaultAsync(p => p.Id == context.EntityId, cancellationToken);

            if (project == null)
                throw new Exception($"Project with ID {context.EntityId} not found");

            var currentUserId = context.CurrentUser.Id;
            PMWorkflowStatusEnum status = context.Action switch
            {
                "Approval" => PMWorkflowStatusEnum.SentForApproval,
                "Review" => PMWorkflowStatusEnum.SentForReview,
                "Reject" => PMWorkflowStatusEnum.ReviewChanges,
                "Approval Changes" => PMWorkflowStatusEnum.ApprovalChanges,
                "Approved" => PMWorkflowStatusEnum.Approved,
                _ => throw new ArgumentException("Unknown action")
            };

            project.WorkflowStatusId = (int)status;
            project.UpdatedAt = DateTime.UtcNow;
            project.UpdatedBy = currentUserId;

            var history = new ProjectWorkflowHistory
            {
                ProjectId = context.EntityId,
                StatusId = (int)status,
                Action = $"Sent for {context.Action}",
                Comments = context.Comments,
                ActionBy = currentUserId,
                AssignedToId = context.AssignedToId
            };

            _context.ProjectWorkflowHistories.Add(history);
            await _context.SaveChangesAsync(cancellationToken);

            return new PMWorkflowDto
            {
                Id = history.Id,
                EntityId = context.EntityId,
                EntityType = EntityType,
                StatusId = history.StatusId,
                Status = history.Action,
                Action = history.Action,
                Comments = history.Comments,
                ActionDate = history.ActionDate,
                ActionBy = currentUserId,
                ActionByName = context.CurrentUser?.UserName ?? "Unknown",
                AssignedToId = context.AssignedToId,
                AssignedToName = context.AssignedToUser?.UserName ?? "Unknown"
            };
        }
    }
}
