using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NJS.Domain.Entities
{
    public class ProjectWorkflowHistory
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int ProjectId { get; set; }

        [Foreign<PERSON><PERSON>("ProjectId")]
        public virtual Project Project { get; set; }

        [Required]
        public int StatusId { get; set; }

        [ForeignKey("StatusId")]
        public virtual PMWorkflowStatus Status { get; set; }

        [Required]
        [StringLength(500)]
        public string Action { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Comments { get; set; }

        [Required]
        public DateTime ActionDate { get; set; } = DateTime.UtcNow;

        [Required]
        [StringLength(450)]
        public string ActionBy { get; set; } = string.Empty;

        [ForeignKey("ActionBy")]
        public virtual User ActionByUser { get; set; }

        [StringLength(450)]
        public string? AssignedToId { get; set; }

        [ForeignKey("AssignedToId")]
        public virtual User? AssignedToUser { get; set; }
    }
}
