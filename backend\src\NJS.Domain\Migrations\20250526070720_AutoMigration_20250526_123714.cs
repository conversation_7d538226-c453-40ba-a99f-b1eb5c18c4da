﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NJS.Domain.Migrations
{
    /// <inheritdoc />
    public partial class AutoMigration_20250526_123714 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_WBSHistories_WBSTaskMonthlyHourHeader_WBSTaskMonthlyHourHeaderId",
                table: "WBSHistories");

            migrationBuilder.AddColumn<int>(
                name: "StatusId",
                table: "WBSTaskMonthlyHourHeader",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "JobStartFormHeaders",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    FormId = table.Column<int>(type: "int", nullable: false),
                    ProjectId = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StatusId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobStartFormHeaders", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobStartFormHeaders_JobStartForms_FormId",
                        column: x => x.FormId,
                        principalTable: "JobStartForms",
                        principalColumn: "FormId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_JobStartFormHeaders_PMWorkflowStatuses_StatusId",
                        column: x => x.StatusId,
                        principalTable: "PMWorkflowStatuses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_JobStartFormHeaders_Projects_ProjectId",
                        column: x => x.ProjectId,
                        principalTable: "Projects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "JobStartFormHistories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    JobStartFormHeaderId = table.Column<int>(type: "int", nullable: false),
                    StatusId = table.Column<int>(type: "int", nullable: false),
                    Action = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Comments = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ActionDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ActionBy = table.Column<string>(type: "nvarchar(450)", maxLength: 450, nullable: true),
                    AssignedToId = table.Column<string>(type: "nvarchar(450)", maxLength: 450, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobStartFormHistories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobStartFormHistories_AspNetUsers_ActionBy",
                        column: x => x.ActionBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_JobStartFormHistories_AspNetUsers_AssignedToId",
                        column: x => x.AssignedToId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_JobStartFormHistories_JobStartFormHeaders_JobStartFormHeaderId",
                        column: x => x.JobStartFormHeaderId,
                        principalTable: "JobStartFormHeaders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_JobStartFormHistories_PMWorkflowStatuses_StatusId",
                        column: x => x.StatusId,
                        principalTable: "PMWorkflowStatuses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_WBSTaskMonthlyHourHeader_StatusId",
                table: "WBSTaskMonthlyHourHeader",
                column: "StatusId");

            migrationBuilder.CreateIndex(
                name: "IX_JobStartFormHeaders_FormId",
                table: "JobStartFormHeaders",
                column: "FormId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_JobStartFormHeaders_ProjectId",
                table: "JobStartFormHeaders",
                column: "ProjectId");

            migrationBuilder.CreateIndex(
                name: "IX_JobStartFormHeaders_StatusId",
                table: "JobStartFormHeaders",
                column: "StatusId");

            migrationBuilder.CreateIndex(
                name: "IX_JobStartFormHistories_ActionBy",
                table: "JobStartFormHistories",
                column: "ActionBy");

            migrationBuilder.CreateIndex(
                name: "IX_JobStartFormHistories_AssignedToId",
                table: "JobStartFormHistories",
                column: "AssignedToId");

            migrationBuilder.CreateIndex(
                name: "IX_JobStartFormHistories_JobStartFormHeaderId",
                table: "JobStartFormHistories",
                column: "JobStartFormHeaderId");

            migrationBuilder.CreateIndex(
                name: "IX_JobStartFormHistories_StatusId",
                table: "JobStartFormHistories",
                column: "StatusId");

            migrationBuilder.AddForeignKey(
                name: "FK_WBSHistories_WBSTaskMonthlyHourHeader_WBSTaskMonthlyHourHeaderId",
                table: "WBSHistories",
                column: "WBSTaskMonthlyHourHeaderId",
                principalTable: "WBSTaskMonthlyHourHeader",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_WBSTaskMonthlyHourHeader_PMWorkflowStatuses_StatusId",
                table: "WBSTaskMonthlyHourHeader",
                column: "StatusId",
                principalTable: "PMWorkflowStatuses",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_WBSHistories_WBSTaskMonthlyHourHeader_WBSTaskMonthlyHourHeaderId",
                table: "WBSHistories");

            migrationBuilder.DropForeignKey(
                name: "FK_WBSTaskMonthlyHourHeader_PMWorkflowStatuses_StatusId",
                table: "WBSTaskMonthlyHourHeader");

            migrationBuilder.DropTable(
                name: "JobStartFormHistories");

            migrationBuilder.DropTable(
                name: "JobStartFormHeaders");

            migrationBuilder.DropIndex(
                name: "IX_WBSTaskMonthlyHourHeader_StatusId",
                table: "WBSTaskMonthlyHourHeader");

            migrationBuilder.DropColumn(
                name: "StatusId",
                table: "WBSTaskMonthlyHourHeader");

            migrationBuilder.AddForeignKey(
                name: "FK_WBSHistories_WBSTaskMonthlyHourHeader_WBSTaskMonthlyHourHeaderId",
                table: "WBSHistories",
                column: "WBSTaskMonthlyHourHeaderId",
                principalTable: "WBSTaskMonthlyHourHeader",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
