import React, { useState, useCallback, useMemo, memo, useRef } from 'react';
import {
    Box,
    Tab,
    Tabs,
    Paper,
    Typography,
    Button,
    CircularProgress,
    Alert,
    Snackbar,
    Container
} from '@mui/material';
import { MonthlyReviewModel, initialFormState } from '../../models/monthlyReviewModel';
import { 
    ActionsTab, 
    BudgetAndScheduleTab, 
    ChangeOrdersTab, 
    ContractAndCostsTab, 
    FinancialDetailsTab, 
    ManpowerPlanningTab,
    ProgressReviewDeliverables
} from './MonthlyProgresscomponents';
import { FormWrapper } from './FormWrapper';

// Constants
const TABS = [
    { id: 0, label: 'Financial Details' },
    { id: 1, label: 'Contract & Costs' },
    { id: 2, label: 'Budget & Schedule' },
    { id: 3, label: 'Manpower Planning' },
    { id: 4, label: 'Review For Deliverable' },
    { id: 5, label: 'Change Orders' },
    { id: 6, label: 'Actions' }
] as const;

const SAVE_DELAY = 1000;
const SNACKBAR_DURATION = 3000;

// Types
interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
}

interface FormState {
    data: MonthlyReviewModel;
    isLoading: boolean;
    showSuccess: boolean;
}

// Memoized TabPanel component
const TabPanel = memo<TabPanelProps>(({ children, value, index }) => (
    <div
        role="tabpanel"
        hidden={value !== index}
        id={`tabpanel-${index}`}
        aria-labelledby={`tab-${index}`}
    >
        {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
));

TabPanel.displayName = 'TabPanel';

// Utility functions
const formatCurrency = (value: number | null): string => {
    if (value == null) return '';
    return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(value);
};

const getCurrentMonthYear = (): string => {
    const date = new Date();
    return `${date.toLocaleString('default', { month: 'long' })} ${date.getFullYear()}`;
};

const calculateTotals = (data: MonthlyReviewModel): MonthlyReviewModel => {
    const newData = { ...data };

    // Calculate fees total
    const net = newData.fees.net || 0;
    const tax = newData.fees.serviceTax || 0;
    newData.fees.total = net + (net * tax / 100);

    // Calculate budget costs subtotal
    const budgetOdcs = newData.budgetCosts.odcs || 0;
    const budgetStaff = newData.budgetCosts.staff || 0;
    newData.budgetCosts.subTotal = budgetOdcs + budgetStaff;

    // Calculate actual costs subtotal
    const actualOdcs = newData.actualCosts.odcs || 0;
    const actualStaff = newData.actualCosts.staff || 0;
    newData.actualCosts.subtotal = actualOdcs + actualStaff;

    // Calculate costs to complete subtotal
    const ctcOdcs = newData.costsToComplete.odcs || 0;
    const ctcStaff = newData.costsToComplete.staff || 0;
    newData.costsToComplete.subtotal = ctcOdcs + ctcStaff;

    // Calculate total EAC estimate
    newData.totalEACEstimate = newData.actualCosts.subtotal + newData.costsToComplete.subtotal;

    // Calculate gross profit percentage
    if (newData.fees.total > 0) {
        newData.grossProfitPercentage = ((newData.fees.total - newData.totalEACEstimate) / newData.fees.total) * 100;
    }

    return newData;
};

const setNestedValue = (obj: any, path: string, value: any): void => {
    const keys = path.split('.');
    let current = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
        if (!(keys[i] in current)) {
            current[keys[i]] = {};
        }
        current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
};

// Custom hook for form state management
const useFormState = () => {
    const [state, setState] = useState<FormState>({
        data: {
            ...initialFormState,
            changeOrders: {
                proposed: [],
                submitted: [],
                approved: []
            },
            lastMonthActions: [...initialFormState.lastMonthActions],
            currentMonthActions: [...initialFormState.currentMonthActions]
        },
        isLoading: false,
        showSuccess: false
    });

    const updateFormData = useCallback((path: string, value: any) => {
        setState(prevState => {
            const newData = { ...prevState.data };
            setNestedValue(newData, path, value);
            
            return {
                ...prevState,
                data: calculateTotals(newData)
            };
        });
    }, []);

    const setLoading = useCallback((loading: boolean) => {
        setState(prev => ({ ...prev, isLoading: loading }));
    }, []);

    const setShowSuccess = useCallback((show: boolean) => {
        setState(prev => ({ ...prev, showSuccess: show }));
    }, []);

    return {
        ...state,
        updateFormData,
        setLoading,
        setShowSuccess
    };
};

// Tab content renderer
const TabContent = memo<{
    tabValue: number;
    formData: MonthlyReviewModel;
    handleInputChange: (path: string, value: any) => void;
    formatCurrency: (value: number | null) => string;
}>(({ tabValue, formData, handleInputChange, formatCurrency }) => {
    const tabComponents = useMemo(() => [
        <FinancialDetailsTab 
            key="financial"
            formData={formData}
            handleInputChange={handleInputChange}
            formatCurrency={formatCurrency}
        />,
        <ContractAndCostsTab 
            key="contract"
            formData={formData}
            handleInputChange={handleInputChange}
            formatCurrency={formatCurrency}
        />,
        <BudgetAndScheduleTab 
            key="budget"
            formData={formData}
            handleInputChange={handleInputChange}
        />,
        <ManpowerPlanningTab 
            key="manpower"
            formData={formData}
            handleInputChange={handleInputChange}
        />,
        <ProgressReviewDeliverables 
            key="deliverables"
            formData={formData}
            handleInputChange={handleInputChange}
        />,
        <ChangeOrdersTab 
            key="change-orders"
            formData={formData}
            handleInputChange={handleInputChange}
        />,
        <ActionsTab 
            key="actions"
            formData={formData}
            handleInputChange={handleInputChange}
        />
    ], [formData, handleInputChange, formatCurrency]);

    return (
        <Box sx={{
            border: '1px solid #e0e0e0',
            borderRadius: '4px',
            overflow: 'hidden',
            mb: 3
        }}>
            {tabComponents.map((component, index) => (
                <TabPanel key={index} value={tabValue} index={index}>
                    {component}
                </TabPanel>
            ))}
        </Box>
    );
});

TabContent.displayName = 'TabContent';

// Navigation buttons component
const NavigationButtons = memo<{
    tabValue: number;
    isLoading: boolean;
    onPrevious: () => void;
    onNext: () => void;
    onSave: () => void;
}>(({ tabValue, isLoading, onPrevious, onNext, onSave }) => {
    const maxTabs = TABS.length - 1;
    
    return (
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3, gap: 2 }}>
            <Button
                variant="outlined"
                onClick={onPrevious}
                disabled={tabValue === 0}
                sx={{
                    borderColor: '#1976d2',
                    color: '#1976d2',
                    '&:hover': {
                        borderColor: '#1565c0',
                        backgroundColor: 'rgba(25, 118, 210, 0.04)'
                    }
                }}
            >
                Previous
            </Button>
            {tabValue < maxTabs ? (
                <Button
                    variant="contained"
                    onClick={onNext}
                    sx={{
                        backgroundColor: '#1976d2',
                        '&:hover': {
                            backgroundColor: '#1565c0'
                        }
                    }}
                >
                    Next
                </Button>
            ) : (
                <Button
                    variant="contained"
                    onClick={onSave}
                    disabled={isLoading}
                    startIcon={isLoading ? <CircularProgress size={20} /> : undefined}
                    sx={{
                        backgroundColor: '#1976d2',
                        '&:hover': {
                            backgroundColor: '#1565c0'
                        }
                    }}
                >
                    {isLoading ? 'Saving...' : 'Save Review'}
                </Button>
            )}
        </Box>
    );
});

NavigationButtons.displayName = 'NavigationButtons';

// Main component
export const MonthlyProgressForm: React.FC = () => {
    const [tabValue, setTabValue] = useState(0);
    const { data: formData, isLoading, showSuccess, updateFormData, setLoading, setShowSuccess } = useFormState();
    
    // Use ref to prevent re-creating the date string on every render
    const currentMonthYear = useRef(getCurrentMonthYear()).current;

    // Memoized handlers
    const handleTabChange = useCallback((_: React.SyntheticEvent, newValue: number) => {
        setTabValue(newValue);
    }, []);

    const handlePrevious = useCallback(() => {
        setTabValue(prev => Math.max(0, prev - 1));
    }, []);

    const handleNext = useCallback(() => {
        setTabValue(prev => Math.min(TABS.length - 1, prev + 1));
    }, []);

    const handleSave = useCallback(async () => {
        setLoading(true);
        try {
            // Simulate API call - replace with actual API call
            await new Promise(resolve => setTimeout(resolve, SAVE_DELAY));
            setShowSuccess(true);
        } catch (error) {
            console.error('Error saving form:', error);
            // Add error handling here
        } finally {
            setLoading(false);
        }
    }, [setLoading, setShowSuccess]);

    const handleSnackbarClose = useCallback(() => {
        setShowSuccess(false);
    }, [setShowSuccess]);

    // Memoized styles
    const tabsStyles = useMemo(() => ({
        '& .MuiTab-root': {
            textTransform: 'none',
            fontWeight: 500,
            color: '#666',
            minWidth: 120,
            '&.Mui-selected': {
                color: '#1976d2'
            }
        },
        '& .MuiTabs-indicator': {
            backgroundColor: '#1976d2'
        }
    }), []);

    const containerStyles = useMemo(() => ({
        width: '100%',
        maxHeight: 'calc(100vh - 200px)',
        overflowY: 'auto',
        overflowX: 'hidden',
        pr: 1,
        pb: 4
    }), []);

    return (
        <FormWrapper>
            <Container maxWidth="xl" sx={{ py: 3 }}>
                <Box sx={containerStyles}>
                    <Paper 
                        elevation={0}
                        sx={{ 
                            p: 3,
                            border: '1px solid #e0e0e0',
                            borderRadius: 1
                        }}
                    >
                        <Typography 
                            variant="h5" 
                            gutterBottom 
                            sx={{ 
                                color: '#1976d2', 
                                fontWeight: 500,
                                mb: 3
                            }}
                        >
                            PMD7. Monthly Progress Review - {currentMonthYear}
                        </Typography>

                        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
                            <Tabs 
                                value={tabValue} 
                                onChange={handleTabChange}
                                variant="scrollable"
                                scrollButtons="auto"
                                allowScrollButtonsMobile
                                sx={tabsStyles}
                            >
                                {TABS.map(tab => (
                                    <Tab 
                                        key={tab.id}
                                        label={tab.label}
                                        id={`tab-${tab.id}`}
                                        aria-controls={`tabpanel-${tab.id}`}
                                    />
                                ))}
                            </Tabs>
                        </Box>

                        <TabContent
                            tabValue={tabValue}
                            formData={formData}
                            handleInputChange={updateFormData}
                            formatCurrency={formatCurrency}
                        />

                        <NavigationButtons
                            tabValue={tabValue}
                            isLoading={isLoading}
                            onPrevious={handlePrevious}
                            onNext={handleNext}
                            onSave={handleSave}
                        />
                    </Paper>

                    <Snackbar
                        open={showSuccess}
                        autoHideDuration={SNACKBAR_DURATION}
                        onClose={handleSnackbarClose}
                        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                    >
                        <Alert 
                            severity="success" 
                            variant="filled"
                            sx={{ backgroundColor: '#1976d2' }}
                        >
                            Review saved successfully!
                        </Alert>
                    </Snackbar>
                </Box>
            </Container>
        </FormWrapper>
    );
};