
import React, { useState } from 'react';
import {
    Paper,
    Typography,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    TextField,
    Button,
    Box
} from '@mui/material';

interface DeliverableRow {
    milestone: string;
    dueDateAsPerContract: string;
    dueDateAsPlanned: string;
    achievedDate: string;
    paymentDueRs: string;
    invoiceDate: string;
    paymentReceived: string;
    commentsIssues: string;
}

const ProgressReviewDeliverables = () => {
    const [deliverables, setDeliverables] = useState<DeliverableRow[]>([
        {
            milestone: 'Part A',
            dueDateAsPerContract: '',
            dueDateAsPlanned: '',
            achievedDate: '',
            paymentDueRs: '',
            invoiceDate: '',
            paymentReceived: '',
            commentsIssues: ''
        },
        {
            milestone: 'Advance payment',
            dueDateAsPerContract: '',
            dueDateAsPlanned: '',
            achievedDate: '',
            paymentDueRs: '300110.00',
            invoiceDate: 'May-14',
            paymentReceived: '',
            commentsIssues: 'Payment is not received against invoice.'
        },
        {
            milestone: 'Inception Report',
            dueDateAsPerContract: '20.03.2014',
            dueDateAsPlanned: '',
            achievedDate: 'May-14',
            paymentDueRs: '150000.00',
            invoiceDate: 'May-14',
            paymentReceived: '',
            commentsIssues: 'Payment is not received against invoice.'
        },
        {
            milestone: 'Detailed Project Report Submission of estimate, bill of quantities',
            dueDateAsPerContract: '30.05.2014',
            dueDateAsPlanned: 'Jan-15',
            achievedDate: 'Feb-15',
            paymentDueRs: '750275.00',
            invoiceDate: 'Feb-15',
            paymentReceived: '',
            commentsIssues: 'Payment is not received against invoice.'
        },
        {
            milestone: '',
            dueDateAsPerContract: '30.06.2014',
            dueDateAsPlanned: 'Jan-15',
            achievedDate: 'Feb-15',
            paymentDueRs: '600220',
            invoiceDate: 'Feb-15',
            paymentReceived: '',
            commentsIssues: 'Payment is not received against invoice.'
        },
        {
            milestone: 'Draft Tender Doc',
            dueDateAsPerContract: '30.06.2014',
            dueDateAsPlanned: 'Feb-15',
            achievedDate: 'Feb-15',
            paymentDueRs: '900330',
            invoiceDate: '',
            paymentReceived: '',
            commentsIssues: 'MCGM to finalize packaging and technology options'
        },
        {
            milestone: 'Tender Evaluation Report',
            dueDateAsPerContract: '15.07.2014',
            dueDateAsPlanned: 'Feb-15',
            achievedDate: '',
            paymentDueRs: '150055',
            invoiceDate: '',
            paymentReceived: '',
            commentsIssues: 'MCGM to finalize packaging and technology options'
        },
        {
            milestone: '',
            dueDateAsPerContract: '',
            dueDateAsPlanned: '',
            achievedDate: '',
            paymentDueRs: '150055',
            invoiceDate: '',
            paymentReceived: '',
            commentsIssues: ''
        },
        {
            milestone: 'Completion of construction work',
            dueDateAsPerContract: '1.08.2014',
            dueDateAsPlanned: 'Mid Mar-15',
            achievedDate: '',
            paymentDueRs: '',
            invoiceDate: '',
            paymentReceived: '',
            commentsIssues: ''
        }
    ]);

    const handleInputChange = (rowIndex: number, field: keyof DeliverableRow, value: string) => {
        const updatedDeliverables = [...deliverables];
        updatedDeliverables[rowIndex] = {
            ...updatedDeliverables[rowIndex],
            [field]: value
        };
        setDeliverables(updatedDeliverables);
    };

    const addNewRow = () => {
        const newRow: DeliverableRow = {
            milestone: '',
            dueDateAsPerContract: '',
            dueDateAsPlanned: '',
            achievedDate: '',
            paymentDueRs: '',
            invoiceDate: '',
            paymentReceived: '',
            commentsIssues: ''
        };
        setDeliverables([...deliverables, newRow]);
    };

    const commonInputStyles = {
        '& .MuiInputBase-root': {
            height: '32px',
            fontSize: '12px'
        },
        '& .MuiOutlinedInput-input': {
            height: '32px',
            padding: '0 8px',
            boxSizing: 'border-box',
            display: 'flex',
            alignItems: 'center',
            fontSize: '12px'
        }
    };

    const headerCellStyle = {
        fontWeight: 'bold',
        fontSize: '11px',
        padding: '4px 8px',
        backgroundColor: '#f5f5f5',
        border: '1px solid #ccc',
        textAlign: 'center' as const,
        lineHeight: '1.2',
        minHeight: '40px',
        verticalAlign: 'middle'
    };

    const dataCellStyle = {
        padding: '2px 4px',
        border: '1px solid #ccc',
        fontSize: '11px',
        lineHeight: '1.2',
        verticalAlign: 'top' as const
    };

    return (
        <Paper elevation={1} sx={{ p: 2 }}>
            <Box sx={{ mb: 2 }}>
                <Typography
                    variant="h6"
                    sx={{
                        fontWeight: 'bold',
                        fontSize: '14px',
                        backgroundColor: '#e8f4f8',
                        padding: '8px',
                        border: '1px solid #ccc',
                        textAlign: 'center',
                        mb: 0
                    }}
                >
                    PROGRESS REVIEW FOR DELIVERABLES
                </Typography>
            </Box>

            <TableContainer sx={{ border: '1px solid #ccc' }}>
                <Table size="small" sx={{ '& .MuiTableCell-root': { border: '1px solid #ccc' } }}>
                    <TableHead>
                        <TableRow>
                            <TableCell sx={{ ...headerCellStyle, width: '20%' }}>
                                Milestone
                            </TableCell>
                            <TableCell sx={{ ...headerCellStyle, width: '12%' }}>
                                Due date as per contract
                            </TableCell>
                            <TableCell sx={{ ...headerCellStyle, width: '12%' }}>
                                Due date as planned
                            </TableCell>
                            <TableCell sx={{ ...headerCellStyle, width: '10%' }}>
                                Achieved Date
                            </TableCell>
                            <TableCell sx={{ ...headerCellStyle, width: '12%' }}>
                                Payment due Rs.
                            </TableCell>
                            <TableCell sx={{ ...headerCellStyle, width: '10%' }}>
                                Invoice date
                            </TableCell>
                            <TableCell sx={{ ...headerCellStyle, width: '12%' }}>
                                Payment Received
                            </TableCell>
                            <TableCell sx={{ ...headerCellStyle, width: '22%' }}>
                                Comments / Issues on Milestone / payment
                            </TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {deliverables.map((row, index) => (
                            <TableRow key={index}>
                                <TableCell sx={dataCellStyle}>
                                    <TextField
                                        size="small"
                                        value={row.milestone}
                                        onChange={(e) => handleInputChange(index, 'milestone', e.target.value)}
                                        fullWidth
                                        multiline
                                        rows={2}
                                        sx={{
                                            ...commonInputStyles,
                                            '& .MuiInputBase-root': {
                                                height: 'auto',
                                                minHeight: '32px'
                                            }
                                        }}
                                        variant="outlined"
                                    />
                                </TableCell>
                                <TableCell sx={dataCellStyle}>
                                    <TextField
                                        size="small"
                                        value={row.dueDateAsPerContract}
                                        onChange={(e) => handleInputChange(index, 'dueDateAsPerContract', e.target.value)}
                                        fullWidth
                                        sx={commonInputStyles}
                                        variant="outlined"
                                    />
                                </TableCell>
                                <TableCell sx={dataCellStyle}>
                                    <TextField
                                        size="small"
                                        value={row.dueDateAsPlanned}
                                        onChange={(e) => handleInputChange(index, 'dueDateAsPlanned', e.target.value)}
                                        fullWidth
                                        sx={commonInputStyles}
                                        variant="outlined"
                                    />
                                </TableCell>
                                <TableCell sx={dataCellStyle}>
                                    <TextField
                                        size="small"
                                        value={row.achievedDate}
                                        onChange={(e) => handleInputChange(index, 'achievedDate', e.target.value)}
                                        fullWidth
                                        sx={commonInputStyles}
                                        variant="outlined"
                                    />
                                </TableCell>
                                <TableCell sx={dataCellStyle}>
                                    <TextField
                                        size="small"
                                        value={row.paymentDueRs}
                                        onChange={(e) => handleInputChange(index, 'paymentDueRs', e.target.value)}
                                        fullWidth
                                        sx={commonInputStyles}
                                        variant="outlined"
                                    />
                                </TableCell>
                                <TableCell sx={dataCellStyle}>
                                    <TextField
                                        size="small"
                                        value={row.invoiceDate}
                                        onChange={(e) => handleInputChange(index, 'invoiceDate', e.target.value)}
                                        fullWidth
                                        sx={commonInputStyles}
                                        variant="outlined"
                                    />
                                </TableCell>
                                <TableCell sx={dataCellStyle}>
                                    <TextField
                                        size="small"
                                        value={row.paymentReceived}
                                        onChange={(e) => handleInputChange(index, 'paymentReceived', e.target.value)}
                                        fullWidth
                                        sx={commonInputStyles}
                                        variant="outlined"
                                    />
                                </TableCell>
                                <TableCell sx={dataCellStyle}>
                                    <TextField
                                        size="small"
                                        value={row.commentsIssues}
                                        onChange={(e) => handleInputChange(index, 'commentsIssues', e.target.value)}
                                        fullWidth
                                        multiline
                                        rows={2}
                                        sx={{
                                            ...commonInputStyles,
                                            '& .MuiInputBase-root': {
                                                height: 'auto',
                                                minHeight: '32px'
                                            }
                                        }}
                                        variant="outlined"
                                    />
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>

            <Button
                variant="outlined"
                onClick={addNewRow}
                sx={{ mt: 2, fontSize: '12px' }}
            >
                Add New Row
            </Button>
        </Paper>
    );
};

export default ProgressReviewDeliverables;
